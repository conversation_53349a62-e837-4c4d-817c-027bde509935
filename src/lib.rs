use zed_extension_api::{self as zed, Action, Result};

struct WhichKeyExtension;

#[derive(<PERSON>lone, serde::Deserialize)]
struct Toggle;

impl Action for Toggle {
    fn name(&self) -> &'static str {
        "which_key::Toggle"
    }
}

impl zed::Extension for WhichKeyExtension {
    fn new() -> Self {
        Self
    }

    fn main(&mut self, cx: &mut zed::ExtensionContext) -> Result<()> {
        cx.register_action(Toggle, |_, _, _| {
            // Logic for the popup window will go here.
            Ok(())
        });

        Ok(())
    }
}

zed::register_extension!(WhichKeyExtension);
