use zed_extension_api::{
    self as zed, Result, <PERSON>lash<PERSON>ommand, <PERSON>lash<PERSON>ommandOutput, SlashCommandOutputSection,
};

struct WhichKeyExtension;

impl zed::Extension for WhichKeyExtension {
    fn new() -> Self {
        Self
    }

    fn run_slash_command(
        &self,
        command: SlashCommand,
        _args: Vec<String>,
        _worktree: Option<&zed::Worktree>,
    ) -> Result<SlashCommandOutput, String> {
        match command.name.as_str() {
            "which-key" => {
                let keybindings = get_vim_keybindings();

                let mut sections = Vec::new();

                // 添加标题
                sections.push(SlashCommandOutputSection {
                    range: zed::Range { start: 0, end: 0 },
                    label: "Which Key - Available Keybindings".to_string(),
                });

                // 添加键绑定信息
                let content = format!("# Which Key - Vim Keybindings\n\n{}", keybindings);

                Ok(SlashCommandOutput {
                    text: content,
                    sections,
                })
            }
            _ => Err(format!("Unknown command: {}", command.name)),
        }
    }
}

fn get_vim_keybindings() -> String {
    let keybindings = vec![
        ("h", "Move left"),
        ("j", "Move down"),
        ("k", "Move up"),
        ("l", "Move right"),
        ("w", "Move to next word"),
        ("b", "Move to previous word"),
        ("e", "Move to end of word"),
        ("0", "Move to beginning of line"),
        ("$", "Move to end of line"),
        ("gg", "Go to first line"),
        ("G", "Go to last line"),
        ("i", "Insert mode"),
        ("a", "Append mode"),
        ("o", "Open new line below"),
        ("O", "Open new line above"),
        ("x", "Delete character"),
        ("dd", "Delete line"),
        ("yy", "Copy line"),
        ("p", "Paste"),
        ("u", "Undo"),
        ("Ctrl+r", "Redo"),
        ("/", "Search"),
        ("n", "Next search result"),
        ("N", "Previous search result"),
        (":", "Command mode"),
        ("v", "Visual mode"),
        ("V", "Visual line mode"),
        ("Ctrl+v", "Visual block mode"),
    ];

    let mut result = String::new();
    result.push_str("## Navigation\n");
    for (key, desc) in &keybindings[0..9] {
        result.push_str(&format!("- **{}**: {}\n", key, desc));
    }

    result.push_str("\n## Editing\n");
    for (key, desc) in &keybindings[9..21] {
        result.push_str(&format!("- **{}**: {}\n", key, desc));
    }

    result.push_str("\n## Search & Command\n");
    for (key, desc) in &keybindings[21..25] {
        result.push_str(&format!("- **{}**: {}\n", key, desc));
    }

    result.push_str("\n## Visual Mode\n");
    for (key, desc) in &keybindings[25..] {
        result.push_str(&format!("- **{}**: {}\n", key, desc));
    }

    result.push_str("\n---\n");
    result.push_str("💡 **Tip**: Use `/which-key` command to show this help anytime!\n");

    result
}

zed::register_extension!(WhichKeyExtension);
